<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>策略动作详情 - 反馈与评论</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            line-height: 1.6;
        }

        .header {
            background-color: #2d2d2d;
            padding: 15px 20px;
            border-bottom: 1px solid #404040;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .back-btn {
            background: none;
            border: none;
            color: #4a90e2;
            font-size: 16px;
            cursor: pointer;
            padding: 5px;
        }

        .back-btn:hover {
            color: #357abd;
        }

        .page-title {
            font-size: 18px;
            font-weight: 600;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .action-detail {
            background-color: #2d2d2d;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid #404040;
        }

        .action-header {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            margin-bottom: 16px;
        }

        .action-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background-color: #4a90e2;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 18px;
            flex-shrink: 0;
        }

        .action-info {
            flex: 1;
        }

        .action-author {
            font-weight: 600;
            font-size: 16px;
            color: #ffffff;
            margin-bottom: 4px;
        }

        .action-role {
            color: #888;
            margin-bottom: 8px;
        }

        .action-time {
            color: #888;
            font-size: 14px;
        }

        .action-content {
            margin-bottom: 20px;
        }

        .action-text {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 16px;
        }

        .action-image {
            max-width: 100%;
            border-radius: 8px;
            margin-bottom: 16px;
        }

        .action-stats {
            display: flex;
            gap: 24px;
            padding: 16px 0;
            border-top: 1px solid #404040;
            font-size: 14px;
            color: #888;
        }

        .comments-container {
            background-color: #2d2d2d;
            border-radius: 12px;
            padding: 24px;
            border: 1px solid #404040;
        }

        .comments-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 1px solid #404040;
        }

        .comment-item {
            margin-bottom: 24px;
            padding-bottom: 20px;
            border-bottom: 1px solid #353535;
        }

        .comment-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .comment-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .comment-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: #5cb85c;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
            flex-shrink: 0;
        }

        .comment-author {
            font-weight: 500;
            color: #ffffff;
        }

        .comment-time {
            color: #888;
            font-size: 14px;
        }

        .comment-text {
            color: #cccccc;
            margin-bottom: 12px;
            line-height: 1.5;
        }

        .comment-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
        }

        .comment-time-info {
            color: #888;
        }

        .comment-reply-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            background: none;
            border: none;
            color: #888;
            cursor: pointer;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .comment-reply-btn:hover {
            color: #4a90e2;
            background-color: #353535;
        }

        .replies {
            margin-top: 16px;
            margin-left: 48px;
        }

        .reply-item {
            background-color: #353535;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 3px solid #4a90e2;
        }

        .reply-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .reply-author {
            font-weight: 500;
            color: #ffffff;
            font-size: 14px;
        }

        .reply-target {
            color: #4a90e2;
            font-size: 14px;
        }

        .reply-time {
            color: #888;
            font-size: 12px;
            margin-left: auto;
        }

        .reply-text {
            color: #cccccc;
            font-size: 14px;
            line-height: 1.4;
        }

        .comment-form {
            background-color: #353535;
            border-radius: 8px;
            padding: 20px;
            margin-top: 24px;
        }

        .form-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-textarea {
            width: 100%;
            min-height: 100px;
            padding: 12px;
            background-color: #404040;
            border: 1px solid #606060;
            color: #ffffff;
            border-radius: 6px;
            resize: vertical;
            font-family: inherit;
        }

        .form-textarea:focus {
            outline: none;
            border-color: #4a90e2;
        }

        .form-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background-color: #4a90e2;
            color: #ffffff;
        }

        .btn-primary:hover {
            background-color: #357abd;
        }

        .btn-secondary {
            background-color: #6c757d;
            color: #ffffff;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
        }

        .image-upload {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .upload-btn {
            background-color: #404040;
            border: 1px solid #606060;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .upload-btn:hover {
            background-color: #4a4a4a;
        }

        .upload-preview {
            max-width: 200px;
            max-height: 120px;
            border-radius: 4px;
            display: none;
        }
    </style>
</head>
<body>
    <div class="header">
        <button class="back-btn" onclick="window.close()">← 返回</button>
        <h1 class="page-title">策略动作详情与反馈</h1>
    </div>

    <div class="container">
        <!-- 策略动作详情 -->
        <div class="action-detail">
            <div class="action-header">
                <div class="action-avatar">经</div>
                <div class="action-info">
                    <div class="action-author">张经理</div>
                    <div class="action-role">经理 · 风控部</div>
                </div>
                <div class="action-time">2小时前</div>
            </div>
            <div class="action-content">
                <div class="action-text">
                    建议立即减仓50%，当前市场波动较大，风险控制为主。请在收盘前完成操作。
                    <br><br>
                    根据最新的技术分析和市场情况，我们需要采取更加谨慎的策略。请各位业务员严格按照风控要求执行，确保资金安全。
                </div>
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjI0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iNDAwIiBoZWlnaHQ9IjI0MCIgZmlsbD0iIzMzMzMzMyIvPgogIDx0ZXh0IHg9IjIwMCIgeT0iMTIwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTgiIGZpbGw9IiNmZmZmZmYiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj7lvZPliY3luILlnLrlm77ooajvvIjnpLrkvovvvIk8L3RleHQ+Cjwvc3ZnPg==" alt="市场图表" class="action-image">
            </div>
            <div class="action-stats">
                <span>💬 12条回复</span>
                <span>👁️ 45次查看</span>
            </div>
        </div>

        <!-- 评论区域 -->
        <div class="comments-container">
            <h3 class="comments-title">反馈与讨论 (12)</h3>
            
            <!-- 评论1 -->
            <div class="comment-item">
                <div class="comment-header">
                    <div class="comment-avatar">李</div>
                    <div class="comment-author">李业务员</div>
                    <div class="comment-time">1小时前</div>
                </div>
                <div class="comment-text">
                    收到，正在执行减仓操作，预计30分钟内完成。目前已经减仓30%，剩余部分正在寻找合适的出场点位。
                </div>
                <div class="comment-actions">
                    <span class="comment-time-info">1小时前</span>
                    <button class="comment-reply-btn">
                        <span>💬</span>
                        <span>回复</span>
                    </button>
                </div>
                <div class="replies">
                    <div class="reply-item">
                        <div class="reply-header">
                            <span class="reply-author">张经理</span>
                            <span class="reply-target">回复 李业务员</span>
                            <span class="reply-time">45分钟前</span>
                        </div>
                        <div class="reply-text">好的，注意控制节奏，不要急于一次性全部减仓。分批操作更安全。</div>
                    </div>
                    <div class="reply-item">
                        <div class="reply-header">
                            <span class="reply-author">李业务员</span>
                            <span class="reply-target">回复 张经理</span>
                            <span class="reply-time">30分钟前</span>
                        </div>
                        <div class="reply-text">明白，我会分3批完成减仓，确保不影响市场价格。</div>
                    </div>
                </div>
            </div>

            <!-- 评论2 -->
            <div class="comment-item">
                <div class="comment-header">
                    <div class="comment-avatar">王</div>
                    <div class="comment-author">王业务员</div>
                    <div class="comment-time">45分钟前</div>
                </div>
                <div class="comment-text">
                    经理，我这边的仓位比较重，是否也需要同步减仓？还是等待进一步指示？
                </div>
                <div class="comment-actions">
                    <span class="comment-time-info">45分钟前</span>
                    <button class="comment-reply-btn">
                        <span>💬</span>
                        <span>回复</span>
                    </button>
                </div>
                <div class="replies">
                    <div class="reply-item">
                        <div class="reply-header">
                            <span class="reply-author">张经理</span>
                            <span class="reply-target">回复 王业务员</span>
                            <span class="reply-time">30分钟前</span>
                        </div>
                        <div class="reply-text">是的，所有重仓位都需要减仓。你的仓位确实偏重，建议减仓60%。</div>
                    </div>
                </div>
            </div>

            <!-- 评论3 -->
            <div class="comment-item">
                <div class="comment-header">
                    <div class="comment-avatar">陈</div>
                    <div class="comment-author">陈业务员</div>
                    <div class="comment-time">20分钟前</div>
                </div>
                <div class="comment-text">
                    已完成减仓操作，减仓幅度55%。请经理查看操作记录。
                </div>
                <div class="comment-actions">
                    <span class="comment-time-info">20分钟前</span>
                    <button class="comment-reply-btn">
                        <span>💬</span>
                        <span>回复</span>
                    </button>
                </div>
            </div>

            <!-- 发表评论表单 -->
            <div class="comment-form">
                <h4 class="form-title">发表反馈</h4>
                <div class="form-group">
                    <textarea class="form-textarea" placeholder="请输入您的反馈或问题..."></textarea>
                </div>
                <div class="image-upload">
                    <input type="file" id="imageUpload" accept="image/*" style="display: none;" onchange="previewImage(this)">
                    <button class="upload-btn" onclick="document.getElementById('imageUpload').click()">📷 上传图片</button>
                    <img id="imagePreview" class="upload-preview" alt="图片预览">
                </div>
                <div class="form-actions">
                    <button class="btn btn-secondary">取消</button>
                    <button class="btn btn-primary" onclick="submitComment()">发表反馈</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function previewImage(input) {
            const preview = document.getElementById('imagePreview');
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        function submitComment() {
            const textarea = document.querySelector('.form-textarea');
            const text = textarea.value.trim();
            
            if (!text) {
                alert('请输入反馈内容');
                return;
            }
            
            // 模拟提交
            alert('反馈已提交！');
            textarea.value = '';
            document.getElementById('imagePreview').style.display = 'none';
            document.getElementById('imageUpload').value = '';
        }

        // 添加点击回复功能
        document.addEventListener('click', function(e) {
            if (e.target.closest('.comment-reply-btn')) {
                const textarea = document.querySelector('.form-textarea');
                const commentItem = e.target.closest('.comment-item');
                if (commentItem) {
                    const commentAuthor = commentItem.querySelector('.comment-author').textContent;
                    textarea.focus();
                    textarea.placeholder = `回复 ${commentAuthor}...`;
                    textarea.value = `@${commentAuthor} `;
                }
            }
        });
    </script>
</body>
</html>
