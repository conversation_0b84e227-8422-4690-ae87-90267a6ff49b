<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账号交易管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            overflow-x: hidden;
        }

        .header {
            background-color: #2d2d2d;
            padding: 10px 20px;
            border-bottom: 1px solid #404040;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo {
            font-weight: bold;
            font-size: 16px;
        }

        .nav-tabs {
            display: flex;
            gap: 10px;
        }

        .nav-tab {
            padding: 8px 16px;
            background-color: #404040;
            border: none;
            color: #ffffff;
            border-radius: 4px;
            cursor: pointer;
        }

        .nav-tab.active {
            background-color: #4a90e2;
        }

        .sidebar {
            position: fixed;
            left: 0;
            top: 60px;
            width: 200px;
            height: calc(100vh - 60px);
            background-color: #2d2d2d;
            border-right: 1px solid #404040;
            padding: 20px 0;
        }

        .sidebar-item {
            padding: 10px 20px;
            cursor: pointer;
            border-left: 3px solid transparent;
        }

        .sidebar-item:hover {
            background-color: #404040;
        }

        .sidebar-item.active {
            background-color: #4a90e2;
            border-left-color: #ffffff;
        }

        .main-content {
            margin-left: 200px;
            padding: 20px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .search-bar {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .search-input {
            padding: 8px 12px;
            background-color: #404040;
            border: 1px solid #606060;
            color: #ffffff;
            border-radius: 4px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-primary {
            background-color: #4a90e2;
            color: #ffffff;
        }

        .btn-success {
            background-color: #5cb85c;
            color: #ffffff;
        }

        .btn-danger {
            background-color: #d9534f;
            color: #ffffff;
        }

        .btn-warning {
            background-color: #f0ad4e;
            color: #ffffff;
        }

        .table-container {
            background-color: #2d2d2d;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #404040;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #404040;
        }

        .table th {
            background-color: #404040;
            font-weight: 600;
        }

        .table tr:hover {
            background-color: #353535;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background-color: #5cb85c;
            color: #ffffff;
        }

        .action-btn {
            background-color: #4a90e2;
            color: #ffffff;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .action-btn:hover {
            background-color: #357abd;
        }

        /* 侧边栏样式 */
        .action-sidebar {
            position: fixed;
            right: -600px;
            top: 0;
            width: 600px;
            height: 100vh;
            background-color: #2d2d2d;
            border-left: 1px solid #404040;
            transition: right 0.3s ease;
            z-index: 1000;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.3);
        }

        .action-sidebar.open {
            right: 0;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
        }

        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .add-btn {
            background-color: #4a90e2;
            color: #ffffff;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .add-btn:hover {
            background-color: #357abd;
        }

        .close-btn {
            background: none;
            border: none;
            color: #ffffff;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
        }

        .sidebar-content {
            padding: 0;
            height: calc(100vh - 80px);
            overflow-y: auto;
        }

        .action-item {
            border-bottom: 1px solid #404040;
            padding: 20px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            position: relative;
        }

        .action-item:hover {
            background-color: #353535;
        }

        .action-item.expanded {
            background-color: #353535;
        }

        .action-tag {
            position: absolute;
            top: 0;
            left: 0;
            width: 70px;
            height: 70px;
            overflow: hidden;
            z-index: 10;
            pointer-events: none;
        }

        .action-tag::after {
            content: attr(data-text);
            position: absolute;
            top: 12px;
            left: -15px;
            width: 80px;
            height: 18px;
            background-color: var(--tag-color);
            color: #ffffff;
            font-size: 10px;
            font-weight: 600;
            text-align: center;
            line-height: 18px;
            transform: rotate(-45deg);
            transform-origin: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .action-tag.strategy {
            --tag-color: #4a90e2;
        }

        .action-tag.feedback {
            --tag-color: #5cb85c;
        }

        .view-more-replies {
            padding: 12px 16px;
            text-align: center;
            color: #4a90e2;
            cursor: pointer;
            font-size: 14px;
            border-top: 1px solid #333333;
            margin-top: 8px;
            transition: background-color 0.2s;
        }

        .view-more-replies:hover {
            background-color: #2a2a2a;
            color: #5ba3f5;
        }

        /* 详情弹窗样式 */
        .detail-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 10000;
            display: none;
        }

        .detail-modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 90%;
            max-width: 800px;
            height: 80%;
            background-color: #1a1a1a;
            border-radius: 12px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .detail-modal-header {
            padding: 20px;
            border-bottom: 1px solid #333333;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .detail-modal-title {
            color: #ffffff;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }

        .detail-modal-close {
            background: none;
            border: none;
            color: #888888;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .detail-modal-close:hover {
            color: #ffffff;
        }

        .detail-modal-body {
            flex: 1;
            overflow-y: auto;
            padding: 0;
        }

        .detail-original-content {
            padding: 20px;
            border-bottom: 1px solid #333333;
        }

        .detail-comments-list {
            padding: 0;
        }

        .detail-comment-item {
            padding: 16px 20px;
            border-bottom: 1px solid #2a2a2a;
            display: flex;
            gap: 12px;
        }

        .detail-comment-item:last-child {
            border-bottom: none;
        }

        .detail-comment-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #4a90e2;
            color: #ffffff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 14px;
            flex-shrink: 0;
        }

        .detail-comment-content {
            flex: 1;
        }

        .detail-comment-author {
            color: #ffffff;
            font-weight: 600;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .detail-comment-text {
            color: #cccccc;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .detail-comment-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .detail-comment-time {
            color: #888888;
            font-size: 12px;
        }

        .detail-comment-reply-btn {
            background: none;
            border: none;
            color: #888888;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .detail-comment-reply-btn:hover {
            background-color: #2a2a2a;
            color: #4a90e2;
        }

        .detail-modal-footer {
            padding: 20px;
            border-top: 1px solid #333333;
            background-color: #1a1a1a;
        }

        .detail-reply-form {
            background-color: #2a2a2a;
            border-radius: 12px;
            padding: 16px;
        }

        .detail-reply-textarea {
            width: 100%;
            min-height: 80px;
            max-height: 200px;
            padding: 12px;
            background-color: #1a1a1a;
            border: 1px solid #333333;
            border-radius: 8px;
            color: #ffffff;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
            outline: none;
            transition: border-color 0.2s;
            margin-bottom: 12px;
        }

        .detail-reply-textarea:focus {
            border-color: #4a90e2;
        }

        .detail-reply-textarea::placeholder {
            color: #888888;
        }

        .detail-reply-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .detail-reply-tools {
            display: flex;
            gap: 8px;
        }

        .detail-tool-btn {
            background: none;
            border: none;
            color: #888888;
            font-size: 16px;
            cursor: pointer;
            padding: 6px;
            border-radius: 4px;
            transition: all 0.2s;
        }

        .detail-tool-btn:hover {
            background-color: #333333;
            color: #ffffff;
        }

        .detail-reply-actions {
            display: flex;
            gap: 8px;
        }

        .detail-cancel-btn {
            background: none;
            border: 1px solid #333333;
            color: #888888;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .detail-cancel-btn:hover {
            border-color: #555555;
            color: #ffffff;
        }

        .detail-submit-btn {
            background-color: #4a90e2;
            border: none;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .detail-submit-btn:hover {
            background-color: #5ba3f5;
        }

        .detail-submit-btn:disabled {
            background-color: #333333;
            color: #888888;
            cursor: not-allowed;
        }

        .action-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 12px;
            margin-top: 24px;
        }

        .action-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #4a90e2;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            flex-shrink: 0;
        }

        .action-info {
            flex: 1;
        }

        .action-author {
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 4px;
        }

        .action-role {
            font-size: 12px;
            color: #888;
            margin-bottom: 8px;
        }

        .action-time {
            font-size: 12px;
            color: #888;
        }

        .action-content {
            margin-bottom: 12px;
        }

        .action-text {
            color: #ffffff;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .action-image {
            max-width: 100%;
            border-radius: 8px;
            margin-top: 8px;
        }

        .action-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #888;
        }

        .stat-left {
            display: flex;
            gap: 20px;
        }

        .stat-item {
            cursor: pointer;
        }

        .stat-item:hover {
            color: #4a90e2;
        }

        .reply-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            background: none;
            border: none;
            color: #888;
            cursor: pointer;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .reply-btn:hover {
            color: #4a90e2;
            background-color: #404040;
        }

        .comments-section {
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #404040;
            display: none;
        }

        .comments-section.show {
            display: block;
        }

        .comment-item {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
            padding: 12px;
            background-color: #404040;
            border-radius: 8px;
        }

        .comment-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #5cb85c;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
            flex-shrink: 0;
        }

        .comment-content {
            flex: 1;
        }

        .comment-author {
            font-weight: 500;
            color: #ffffff;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .comment-text {
            color: #cccccc;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 8px;
        }

        .comment-actions {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 12px;
            margin-top: 8px;
        }

        .comment-time-info {
            color: #888;
        }

        .comment-reply-btn {
            display: flex;
            align-items: center;
            gap: 4px;
            background: none;
            border: none;
            color: #888;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .comment-reply-btn:hover {
            color: #4a90e2;
            background-color: #404040;
        }

        .comment-reply-form {
            margin-top: 12px;
            padding: 12px;
            background-color: #2a2a2a;
            border-radius: 8px;
            display: none;
        }

        .comment-reply-form.show {
            display: block;
        }

        .comment-reply-textarea {
            width: 100%;
            min-height: 60px;
            max-height: 120px;
            padding: 8px 12px;
            background-color: #1a1a1a;
            border: 1px solid #333333;
            border-radius: 6px;
            color: #ffffff;
            font-size: 13px;
            line-height: 1.4;
            resize: none;
            outline: none;
            transition: border-color 0.2s;
            margin-bottom: 8px;
        }

        .comment-reply-textarea:focus {
            border-color: #4a90e2;
        }

        .comment-reply-textarea::placeholder {
            color: #888888;
        }

        .comment-reply-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .comment-reply-tools {
            display: flex;
            gap: 6px;
        }

        .comment-tool-btn {
            background: none;
            border: none;
            color: #888888;
            font-size: 14px;
            cursor: pointer;
            padding: 4px;
            border-radius: 3px;
            transition: all 0.2s;
        }

        .comment-tool-btn:hover {
            background-color: #333333;
            color: #ffffff;
        }

        .comment-reply-actions {
            display: flex;
            gap: 6px;
        }

        .comment-cancel-btn {
            background: none;
            border: 1px solid #333333;
            color: #888888;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .comment-cancel-btn:hover {
            border-color: #555555;
            color: #ffffff;
        }

        .comment-submit-btn {
            background-color: #4a90e2;
            border: none;
            color: #ffffff;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .comment-submit-btn:hover {
            background-color: #5ba3f5;
        }

        .comment-submit-btn:disabled {
            background-color: #333333;
            color: #888888;
            cursor: not-allowed;
        }

        .reply-item {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
            padding: 12px;
            background-color: #404040;
            border-radius: 8px;
        }

        .reply-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #5cb85c;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
            flex-shrink: 0;
        }

        .reply-content {
            flex: 1;
        }

        .reply-author {
            font-weight: 500;
            color: #ffffff;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .reply-target {
            color: #888;
            font-size: 14px;
        }

        .reply-text {
            color: #cccccc;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 8px;
        }

        .reply-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
        }

        .reply-time-info {
            color: #888;
        }

        .reply-reply-btn {
            display: flex;
            align-items: center;
            gap: 4px;
            background: none;
            border: none;
            color: #888;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .reply-reply-btn:hover {
            color: #4a90e2;
            background-color: #353535;
        }

        .view-more {
            text-align: center;
            padding: 12px;
            color: #4a90e2;
            cursor: pointer;
            font-size: 14px;
            border-top: 1px solid #404040;
        }

        .view-more:hover {
            background-color: #353535;
        }

        /* 回复输入框样式 */
        .reply-form {
            margin-top: 12px;
            padding: 16px;
            background-color: #404040;
            border-radius: 8px;
            border: 1px solid #606060;
            display: none;
        }

        .reply-form.show {
            display: block;
        }

        .reply-input {
            width: 100%;
            min-height: 80px;
            padding: 12px;
            background-color: #353535;
            border: 1px solid #606060;
            color: #ffffff;
            border-radius: 6px;
            resize: vertical;
            font-family: inherit;
            font-size: 14px;
            margin-bottom: 12px;
        }

        .reply-input:focus {
            outline: none;
            border-color: #4a90e2;
        }

        .reply-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .reply-tools {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .tool-btn {
            background: none;
            border: none;
            color: #888;
            cursor: pointer;
            padding: 6px;
            border-radius: 4px;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .tool-btn:hover {
            color: #4a90e2;
            background-color: #353535;
        }

        .reply-actions {
            display: flex;
            gap: 8px;
        }

        .reply-submit {
            background-color: #4a90e2;
            color: #ffffff;
            border: none;
            padding: 6px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
        }

        .reply-submit:hover {
            background-color: #357abd;
        }

        .reply-cancel {
            background: none;
            border: none;
            color: #888;
            cursor: pointer;
            padding: 6px 12px;
            font-size: 12px;
        }

        .reply-cancel:hover {
            color: #ffffff;
        }

        /* 新增策略动作弹窗样式 */
        .add-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 2000;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .add-modal.show {
            display: flex;
        }

        .add-modal-content {
            background-color: #2d2d2d;
            border-radius: 12px;
            width: 500px;
            max-width: 90vw;
            max-height: 80vh;
            overflow-y: auto;
            border: 1px solid #404040;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }

        .add-modal-header {
            padding: 20px;
            border-bottom: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .add-modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
        }

        .add-modal-close {
            background: none;
            border: none;
            color: #ffffff;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
        }

        .add-modal-body {
            padding: 20px;
        }

        .add-form-group {
            margin-bottom: 20px;
        }

        .add-form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #ffffff;
        }

        .add-form-textarea {
            width: 100%;
            min-height: 120px;
            padding: 12px;
            background-color: #404040;
            border: 1px solid #606060;
            color: #ffffff;
            border-radius: 6px;
            resize: vertical;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.5;
        }

        .add-form-textarea:focus {
            outline: none;
            border-color: #4a90e2;
        }

        .add-form-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 16px;
        }

        .add-form-tools {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .add-tool-btn {
            background: none;
            border: none;
            color: #888;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            font-size: 18px;
            transition: all 0.2s ease;
        }

        .add-tool-btn:hover {
            color: #4a90e2;
            background-color: #404040;
        }

        .add-form-actions {
            display: flex;
            gap: 12px;
        }

        .add-submit-btn {
            background-color: #4a90e2;
            color: #ffffff;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }

        .add-submit-btn:hover {
            background-color: #357abd;
        }

        .add-cancel-btn {
            background: none;
            border: 1px solid #606060;
            color: #ffffff;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .add-cancel-btn:hover {
            background-color: #404040;
        }

        .add-image-preview {
            margin-top: 12px;
            max-width: 100%;
            border-radius: 6px;
            display: none;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .overlay.show {
            opacity: 1;
            visibility: visible;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="header">
        <div class="logo">🎯 先机系统</div>
        <div class="nav-tabs">
            <button class="nav-tab">首页</button>
            <button class="nav-tab">共享</button>
            <button class="nav-tab active">策略</button>
        </div>
    </div>

    <!-- 左侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-item">📊 数据分析</div>
        <div class="sidebar-item">🔍 策略分析</div>
        <div class="sidebar-item">🏠 首页</div>
        <div class="sidebar-item active">📈 持仓</div>
        <div class="sidebar-item">💰 资金</div>
        <div class="sidebar-item">📋 委托</div>
        <div class="sidebar-item">📊 小组</div>
        <div class="sidebar-item">⚙️ 设置</div>
        <div class="sidebar-item">📱 策略配置</div>
        <div class="sidebar-item">📊 持仓分析</div>
        <div class="sidebar-item">💹 基础分析</div>
        <div class="sidebar-item">📈 市场分析</div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-header">
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="搜索账号...">
                <button class="btn btn-primary">搜索</button>
            </div>
            <div>
                <button class="btn btn-success">新增</button>
                <button class="btn btn-danger">删除</button>
                <button class="btn btn-warning">导出</button>
            </div>
        </div>

        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>账号名称</th>
                        <th>账号类型</th>
                        <th>账号状态</th>
                        <th>交易类型</th>
                        <th>账号余额</th>
                        <th>可用余额</th>
                        <th>冻结金额</th>
                        <th>今日盈亏</th>
                        <th>总盈亏</th>
                        <th>最后交易时间</th>
                        <th>操作</th>
                        <th>账号指令</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>153</td>
                        <td>💰 账户_120000_2024...</td>
                        <td><span class="status-badge status-active">主账户</span></td>
                        <td><span class="status-badge status-active">正常</span></td>
                        <td><span class="status-badge status-active">现货</span></td>
                        <td>120000.00</td>
                        <td>118500.00</td>
                        <td>1500.00</td>
                        <td>+2500.00</td>
                        <td>📈 +8500.00</td>
                        <td>2025-07-04 10:40:26</td>
                        <td>
                            <button class="btn btn-primary btn-sm">编辑</button>
                            <button class="btn btn-danger btn-sm">删除</button>
                        </td>
                        <td>
                            <button class="action-btn" onclick="openActionSidebar()">账号指令</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 遮罩层 -->
    <div class="overlay" id="overlay" onclick="closeActionSidebar()"></div>

    <!-- 策略动作侧边栏 -->
    <div class="action-sidebar" id="actionSidebar">
        <div class="sidebar-header">
            <h3 class="sidebar-title">账号策略动作</h3>
            <div class="header-actions">
                <button class="add-btn" onclick="showAddModal()">新增</button>
                <button class="close-btn" onclick="closeActionSidebar()">&times;</button>
            </div>
        </div>
        <div class="sidebar-content">
            <!-- 策略动作1 -->
            <div class="action-item" onclick="toggleComments(1)">
                <div class="action-tag strategy" data-text="策略动作"></div>
                <div class="action-header">
                    <div class="action-avatar">经</div>
                    <div class="action-info">
                        <div class="action-author">张经理</div>
                        <div class="action-role">经理 · 账户管理部</div>
                    </div>
                    <div class="action-time">2小时前</div>
                </div>
                <div class="action-content">
                    <div class="action-text">账户153需要进行资金调整，当前账户余额过高，建议进行分散管理。请在今日内完成以下操作：转出50%资金到备用账户，调整单笔交易限额至10000，开启自动止损功能。</div>
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgZmlsbD0iIzMzMzMzMyIvPgogIDx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2ZmZmZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuW9k+WJjeW4guWcuuWbvuihqDwvdGV4dD4KPC9zdmc+" alt="市场图表" class="action-image">
                </div>
                <div class="action-stats">
                    <div class="stat-left">
                        <span class="stat-item">💬 3条回复</span>
                    </div>
                    <button class="reply-btn" onclick="showActionReply(1, event)">
                        <span>💬</span>
                        <span>回复</span>
                    </button>
                </div>
                <div class="comments-section" id="comments-1">
                    <div class="comment-item">
                        <div class="comment-avatar">李</div>
                        <div class="comment-content">
                            <div class="comment-author">李业务员</div>
                            <div class="comment-text">收到，正在执行资金转移操作，预计30分钟内完成账户调整。</div>
                            <div class="comment-actions">
                                <span class="comment-time-info">1小时前</span>
                                <button class="comment-reply-btn" onclick="showCommentReply(this, event)">
                                    <span>💬</span>
                                    <span>回复</span>
                                </button>
                            </div>
                            <div class="comment-reply-form">
                                <textarea class="comment-reply-textarea" placeholder="回复 李业务员..."></textarea>
                                <div class="comment-reply-toolbar">
                                    <div class="comment-reply-tools">
                                        <button class="comment-tool-btn" title="上传图片">📷</button>
                                    </div>
                                    <div class="comment-reply-actions">
                                        <button class="comment-cancel-btn" onclick="hideCommentReply(this)">取消</button>
                                        <button class="comment-submit-btn" onclick="submitCommentReply(this)">回复</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="comment-item">
                        <div class="comment-avatar">经</div>
                        <div class="comment-content">
                            <div class="comment-author">张经理 > 李业务员</div>
                            <div class="comment-text">好的，注意分批转移，不要一次性转移全部资金，确保账户正常运行。</div>
                            <div class="comment-actions">
                                <span class="comment-time-info">50分钟前</span>
                                <button class="comment-reply-btn" onclick="showCommentReply(this, event)">
                                    <span>💬</span>
                                    <span>回复</span>
                                </button>
                            </div>
                            <div class="comment-reply-form">
                                <textarea class="comment-reply-textarea" placeholder="回复 张经理..."></textarea>
                                <div class="comment-reply-toolbar">
                                    <div class="comment-reply-tools">
                                        <button class="comment-tool-btn" title="上传图片">📷</button>
                                    </div>
                                    <div class="comment-reply-actions">
                                        <button class="comment-cancel-btn" onclick="hideCommentReply(this)">取消</button>
                                        <button class="comment-submit-btn" onclick="submitCommentReply(this)">回复</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="comment-item">
                        <div class="comment-avatar">王</div>
                        <div class="comment-content">
                            <div class="comment-author">王业务员</div>
                            <div class="comment-text">经理，我负责的账户余额也比较高，是否也需要同步进行资金调整？</div>
                            <div class="comment-actions">
                                <span class="comment-time-info">45分钟前</span>
                                <button class="comment-reply-btn" onclick="showCommentReply(this, event)">
                                    <span>💬</span>
                                    <span>回复</span>
                                </button>
                            </div>
                            <div class="comment-reply-form">
                                <textarea class="comment-reply-textarea" placeholder="回复 王业务员..."></textarea>
                                <div class="comment-reply-toolbar">
                                    <div class="comment-reply-tools">
                                        <button class="comment-tool-btn" title="上传图片">📷</button>
                                    </div>
                                    <div class="comment-reply-actions">
                                        <button class="comment-cancel-btn" onclick="hideCommentReply(this)">取消</button>
                                        <button class="comment-submit-btn" onclick="submitCommentReply(this)">回复</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="comment-item">
                        <div class="comment-avatar">经</div>
                        <div class="comment-content">
                            <div class="comment-author">张经理 > 王业务员</div>
                            <div class="comment-text">是的，所有高额度账户都需要调整。你负责的账户确实余额偏高，建议转出60%资金。</div>
                            <div class="comment-actions">
                                <span class="comment-time-info">30分钟前</span>
                                <button class="comment-reply-btn" onclick="showCommentReply(this, event)">
                                    <span>💬</span>
                                    <span>回复</span>
                                </button>
                            </div>
                            <div class="comment-reply-form">
                                <textarea class="comment-reply-textarea" placeholder="回复 张经理..."></textarea>
                                <div class="comment-reply-toolbar">
                                    <div class="comment-reply-tools">
                                        <button class="comment-tool-btn" title="上传图片">📷</button>
                                    </div>
                                    <div class="comment-reply-actions">
                                        <button class="comment-cancel-btn" onclick="hideCommentReply(this)">取消</button>
                                        <button class="comment-submit-btn" onclick="submitCommentReply(this)">回复</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 查看更多回复链接 -->
                    <div class="view-more-replies" onclick="openDetailModal(1, event)">
                        查看更多回复 >>
                    </div>
                </div>
            </div>

            <!-- 策略动作2 -->
            <div class="action-item" onclick="toggleComments(2)">
                <div class="action-tag strategy" data-text="策略动作"></div>
                <div class="action-header">
                    <div class="action-avatar">组</div>
                    <div class="action-info">
                        <div class="action-author">刘组长</div>
                        <div class="action-role">组长 · 账户运营组</div>
                    </div>
                    <div class="action-time">4小时前</div>
                </div>
                <div class="action-content">
                    <div class="action-text">批量账户需要进行安全升级，请协助完成所有子账户的密码重置和双重验证设置。优先处理高额度账户。</div>
                </div>
                <div class="action-stats">
                    <div class="stat-left">
                        <span class="stat-item">💬 5条回复</span>
                    </div>
                    <button class="reply-btn" onclick="showActionReply(2, event)">
                        <span>💬</span>
                        <span>回复</span>
                    </button>
                </div>
                <div class="comments-section" id="comments-2">
                    <div class="comment-item">
                        <div class="comment-avatar">陈</div>
                        <div class="comment-content">
                            <div class="comment-author">陈业务员</div>
                            <div class="comment-text">已经开始处理高额度账户的安全升级，预计今天能完成一半。</div>
                            <div class="comment-actions">
                                <span class="comment-time-info">3小时前</span>
                                <button class="comment-reply-btn" onclick="showCommentReply(this, event)">
                                    <span>💬</span>
                                    <span>回复</span>
                                </button>
                            </div>
                            <div class="comment-reply-form">
                                <textarea class="comment-reply-textarea" placeholder="回复 陈业务员..."></textarea>
                                <div class="comment-reply-toolbar">
                                    <div class="comment-reply-tools">
                                        <button class="comment-tool-btn" title="上传图片">📷</button>
                                    </div>
                                    <div class="comment-reply-actions">
                                        <button class="comment-cancel-btn" onclick="hideCommentReply(this)">取消</button>
                                        <button class="comment-submit-btn" onclick="submitCommentReply(this)">回复</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="comment-item">
                        <div class="comment-avatar">赵</div>
                        <div class="comment-content">
                            <div class="comment-author">赵业务员</div>
                            <div class="comment-text">组长，双重验证设置比较复杂，需要用户配合，进度可能会慢一些。</div>
                            <div class="comment-actions">
                                <span class="comment-time-info">2小时前</span>
                                <button class="comment-reply-btn" onclick="showCommentReply(this, event)">
                                    <span>💬</span>
                                    <span>回复</span>
                                </button>
                            </div>
                            <div class="comment-reply-form">
                                <textarea class="comment-reply-textarea" placeholder="回复 赵业务员..."></textarea>
                                <div class="comment-reply-toolbar">
                                    <div class="comment-reply-tools">
                                        <button class="comment-tool-btn" title="上传图片">📷</button>
                                    </div>
                                    <div class="comment-reply-actions">
                                        <button class="comment-cancel-btn" onclick="hideCommentReply(this)">取消</button>
                                        <button class="comment-submit-btn" onclick="submitCommentReply(this)">回复</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 查看更多回复链接 -->
                    <div class="view-more-replies" onclick="openDetailModal(2, event)">
                        查看更多回复 >>
                    </div>
                </div>
            </div>

            <!-- 策略动作3 -->
            <div class="action-item" onclick="toggleComments(3)">
                <div class="action-tag feedback" data-text="跟进反馈"></div>
                <div class="action-header">
                    <div class="action-avatar">经</div>
                    <div class="action-info">
                        <div class="action-author">张经理</div>
                        <div class="action-role">经理 · 账户管理部</div>
                    </div>
                    <div class="action-time">1天前</div>
                </div>
                <div class="action-content">
                    <div class="action-text">本周账户运营总结：账户153资金调整已完成，备用账户激活成功。下周重点关注大额账户的交易行为监控。</div>
                </div>
                <div class="action-stats">
                    <div class="stat-left">
                        <span class="stat-item">💬 8条回复</span>
                    </div>
                    <button class="reply-btn" onclick="showActionReply(3, event)">
                        <span>💬</span>
                        <span>回复</span>
                    </button>
                </div>
                <div class="comments-section" id="comments-3">
                    <div class="comment-item">
                        <div class="comment-avatar">李</div>
                        <div class="comment-content">
                            <div class="comment-author">李业务员</div>
                            <div class="comment-text">这周账户运营确实很顺利，感谢经理的指导！</div>
                            <div class="comment-actions">
                                <span class="comment-time-info">1天前</span>
                                <button class="comment-reply-btn" onclick="showCommentReply(this, event)">
                                    <span>💬</span>
                                    <span>回复</span>
                                </button>
                            </div>
                            <div class="comment-reply-form">
                                <textarea class="comment-reply-textarea" placeholder="回复 李业务员..."></textarea>
                                <div class="comment-reply-toolbar">
                                    <div class="comment-reply-tools">
                                        <button class="comment-tool-btn" title="上传图片">📷</button>
                                    </div>
                                    <div class="comment-reply-actions">
                                        <button class="comment-cancel-btn" onclick="hideCommentReply(this)">取消</button>
                                        <button class="comment-submit-btn" onclick="submitCommentReply(this)">回复</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 查看更多回复链接 -->
                    <div class="view-more-replies" onclick="openDetailModal(3, event)">
                        查看更多回复 >>
                    </div>
                </div>
            </div>
        </div>
        <div class="view-more" onclick="openDetailPage()">
            查看更多反馈
        </div>
    </div>

    <!-- 新增策略动作弹窗 -->
    <div class="add-modal" id="addModal">
        <div class="add-modal-content">
            <div class="add-modal-header">
                <h3 class="add-modal-title">新增内容</h3>
                <button class="add-modal-close" onclick="hideAddModal()">&times;</button>
            </div>
            <div class="add-modal-body">
                <div class="add-form-group">
                    <label class="add-form-label">类型选择</label>
                    <div style="display: flex; gap: 16px; margin-bottom: 16px;">
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="radio" name="actionType" value="strategy" checked style="margin: 0;">
                            <span style="color: #4a90e2;">操作指令</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="radio" name="actionType" value="feedback" style="margin: 0;">
                            <span style="color: #5cb85c;">跟进反馈</span>
                        </label>
                    </div>
                </div>
                <div class="add-form-group">
                    <label class="add-form-label">内容</label>
                    <textarea class="add-form-textarea" id="addActionContent" placeholder="请输入具体内容..."></textarea>
                    <img id="addImagePreview" class="add-image-preview" alt="图片预览">
                </div>
                <div class="add-form-toolbar">
                    <div class="add-form-tools">
                        <button class="add-tool-btn" onclick="uploadAddImage()" title="上传图片">📷</button>
                        <input type="file" id="addImageUpload" accept="image/*" style="display: none;" onchange="handleAddImageUpload(this)">
                    </div>
                    <div class="add-form-actions">
                        <button class="add-cancel-btn" onclick="hideAddModal()">取消</button>
                        <button class="add-submit-btn" onclick="submitAddAction()">发布</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情弹窗 -->
    <div class="detail-modal" id="detailModal">
        <div class="detail-modal-content">
            <div class="detail-modal-header">
                <h3 class="detail-modal-title" id="detailModalTitle">账号操作指令详情</h3>
                <button class="detail-modal-close" onclick="closeDetailModal()">&times;</button>
            </div>
            <div class="detail-modal-body">
                <!-- 原始内容 -->
                <div class="detail-original-content" id="detailOriginalContent">
                    <!-- 动态填充原始内容 -->
                </div>
                <!-- 评论列表 -->
                <div class="detail-comments-list" id="detailCommentsList">
                    <!-- 动态填充评论 -->
                </div>
            </div>
            <div class="detail-modal-footer">
                <div class="detail-reply-form">
                    <textarea class="detail-reply-textarea" id="detailInput" placeholder="理性发言，友善互动"></textarea>
                    <div class="detail-reply-toolbar">
                        <div class="detail-reply-tools">
                            <button class="detail-tool-btn" onclick="uploadDetailImage()" title="上传图片">📷</button>
                        </div>
                        <div class="detail-reply-actions">
                            <button class="detail-cancel-btn" onclick="clearDetailInput()">取消</button>
                            <button class="detail-submit-btn" onclick="sendDetailComment()">发布</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openActionSidebar() {
            document.getElementById('actionSidebar').classList.add('open');
            document.getElementById('overlay').classList.add('show');
        }

        function closeActionSidebar() {
            document.getElementById('actionSidebar').classList.remove('open');
            document.getElementById('overlay').classList.remove('show');
            // 关闭所有展开的评论
            document.querySelectorAll('.comments-section').forEach(section => {
                section.classList.remove('show');
            });
            document.querySelectorAll('.action-item').forEach(item => {
                item.classList.remove('expanded');
            });
        }

        function toggleComments(actionId) {
            const commentsSection = document.getElementById(`comments-${actionId}`);
            const actionItem = commentsSection.closest('.action-item');

            // 关闭其他展开的评论
            document.querySelectorAll('.comments-section').forEach(section => {
                if (section.id !== `comments-${actionId}`) {
                    section.classList.remove('show');
                }
            });
            document.querySelectorAll('.action-item').forEach(item => {
                if (item !== actionItem) {
                    item.classList.remove('expanded');
                }
            });

            // 切换当前评论的显示状态
            commentsSection.classList.toggle('show');
            actionItem.classList.toggle('expanded');

            // 阻止事件冒泡
            event.stopPropagation();
        }

        function openDetailPage() {
            // 打开新标签页显示详细的反馈页面
            const detailUrl = 'detail.html';
            window.open(detailUrl, '_blank');
        }

        function showActionReply(actionId, event) {
            event.stopPropagation();

            // 关闭其他回复框
            document.querySelectorAll('.reply-form').forEach(form => {
                form.classList.remove('show');
            });

            // 查找或创建回复框
            const actionItem = event.target.closest('.action-item');
            let replyForm = actionItem.querySelector('.reply-form');

            if (!replyForm) {
                replyForm = createReplyForm();
                actionItem.appendChild(replyForm);
            }

            replyForm.classList.add('show');
            replyForm.querySelector('.reply-input').focus();
        }

        function showCommentReply(button, event) {
            event.stopPropagation();

            // 隐藏所有其他回复框
            document.querySelectorAll('.comment-reply-form.show').forEach(form => {
                form.classList.remove('show');
            });

            // 显示当前评论的回复框
            const commentContent = button.closest('.comment-content');
            const replyForm = commentContent.querySelector('.comment-reply-form');
            if (replyForm) {
                replyForm.classList.add('show');

                // 聚焦到输入框
                const textarea = replyForm.querySelector('.comment-reply-textarea');
                textarea.focus();
            }
        }

        function hideCommentReply(button) {
            const replyForm = button.closest('.comment-reply-form');
            replyForm.classList.remove('show');

            // 清空输入框
            const textarea = replyForm.querySelector('.comment-reply-textarea');
            textarea.value = '';
            textarea.style.height = 'auto';
        }

        function submitCommentReply(button) {
            const replyForm = button.closest('.comment-reply-form');
            const textarea = replyForm.querySelector('.comment-reply-textarea');
            const content = textarea.value.trim();

            if (!content) {
                alert('请输入回复内容');
                return;
            }

            // 模拟提交回复
            console.log('提交回复:', content);

            // 隐藏回复框
            hideCommentReply(button);

            // 这里可以添加实际的提交逻辑
            alert('回复已提交');
        }

        function createReplyForm() {
            const form = document.createElement('div');
            form.className = 'reply-form';
            form.innerHTML = `
                <textarea class="reply-input" placeholder="写下你的回复..."></textarea>
                <div class="reply-toolbar">
                    <div class="reply-tools">
                        <button class="tool-btn" onclick="uploadImage(this)" title="上传图片">📷</button>
                        <button class="tool-btn" title="表情">😊</button>
                        <button class="tool-btn" title="提及">@</button>
                    </div>
                    <div class="reply-actions">
                        <button class="reply-cancel" onclick="hideReplyForm(this)">取消</button>
                        <button class="reply-submit" onclick="submitReply(this)">发布</button>
                    </div>
                </div>
                <input type="file" accept="image/*" style="display: none;" onchange="handleImageUpload(this)">
            `;
            return form;
        }

        function hideReplyForm(button) {
            const form = button.closest('.reply-form');
            form.classList.remove('show');
            form.querySelector('.reply-input').value = '';
        }

        function submitReply(button) {
            const form = button.closest('.reply-form');
            const input = form.querySelector('.reply-input');
            const text = input.value.trim();

            if (!text) {
                alert('请输入回复内容');
                return;
            }

            // 模拟提交
            alert('回复已发布！');
            hideReplyForm(button);
        }

        function uploadImage(button) {
            const form = button.closest('.reply-form');
            const fileInput = form.querySelector('input[type="file"]');
            fileInput.click();
        }

        function handleImageUpload(input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];
                alert(`图片 "${file.name}" 已选择，将在发布时上传`);
            }
        }

        // 新增策略动作相关函数
        function showAddModal() {
            document.getElementById('addModal').classList.add('show');
            document.body.style.overflow = 'hidden';
            document.getElementById('addActionContent').focus();
        }

        function hideAddModal() {
            document.getElementById('addModal').classList.remove('show');
            document.body.style.overflow = 'auto';
            // 清空表单
            document.getElementById('addActionContent').value = '';
            document.getElementById('addImagePreview').style.display = 'none';
            document.getElementById('addImageUpload').value = '';
        }

        function uploadAddImage() {
            document.getElementById('addImageUpload').click();
        }

        function handleAddImageUpload(input) {
            const preview = document.getElementById('addImagePreview');
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        function submitAddAction() {
            const content = document.getElementById('addActionContent').value.trim();
            const actionType = document.querySelector('input[name="actionType"]:checked').value;
            const typeName = actionType === 'strategy' ? '操作指令' : '跟进反馈';

            if (!content) {
                alert('请输入内容');
                return;
            }

            // 模拟提交
            alert(`${typeName}已发布！`);
            hideAddModal();

            // 这里可以添加实际的提交逻辑
            // 例如：将新的内容添加到侧边栏列表中
            // addNewActionToSidebar(content, actionType);
        }

        // 可选：动态添加新内容到侧边栏的函数
        function addNewActionToSidebar(content, type) {
            const sidebarContent = document.querySelector('.sidebar-content');
            const tagClass = type === 'strategy' ? 'strategy' : 'feedback';
            const tagText = type === 'strategy' ? '操作指令' : '跟进反馈';
            const actionId = Date.now(); // 简单的ID生成

            const newActionHTML = `
                <div class="action-item" onclick="toggleComments(${actionId})">
                    <div class="action-tag ${tagClass}" data-text="${tagText}"></div>
                    <div class="action-header">
                        <div class="action-avatar">我</div>
                        <div class="action-info">
                            <div class="action-author">我</div>
                            <div class="action-role">当前用户</div>
                            <div class="action-time">刚刚</div>
                        </div>
                    </div>
                    <div class="action-content">
                        <div class="action-text">${content}</div>
                    </div>
                    <div class="action-stats">
                        <div class="stat-left">
                            <span class="stat-item">💬 0条回复</span>
                        </div>
                        <button class="reply-btn" onclick="showActionReply(${actionId}, event)">
                            <span>💬</span>
                            <span>回复</span>
                        </button>
                    </div>
                    <div class="comments-section" id="comments-${actionId}" style="display: none;">
                        <div class="comments-list">
                            <!-- 暂无评论 -->
                        </div>
                    </div>
                </div>
            `;

            sidebarContent.insertAdjacentHTML('afterbegin', newActionHTML);
        }

        // 打开详情弹窗
        function openDetailModal(actionId, event) {
            event.stopPropagation();

            // 模拟数据 - 实际应用中应该从服务器获取
            const actionData = getActionData(actionId);
            const comments = getActionComments(actionId);

            // 填充原始内容
            document.getElementById('detailOriginalContent').innerHTML = `
                <div class="action-header" style="margin-bottom: 16px;">
                    <div class="action-avatar">${actionData.avatar}</div>
                    <div class="action-info">
                        <div class="action-author">${actionData.author}</div>
                        <div class="action-role">${actionData.role}</div>
                    </div>
                    <div class="action-time">${actionData.time}</div>
                </div>
                <div class="action-content">
                    <div class="action-text">${actionData.content}</div>
                    ${actionData.image ? `<img src="${actionData.image}" alt="附图" class="action-image" style="margin-top: 12px;">` : ''}
                </div>
            `;

            // 填充评论列表
            const commentsHtml = comments.map(comment => `
                <div class="detail-comment-item">
                    <div class="detail-comment-avatar">${comment.avatar}</div>
                    <div class="detail-comment-content">
                        <div class="detail-comment-author">${comment.author}</div>
                        <div class="detail-comment-text">${comment.text}</div>
                        <div class="detail-comment-actions">
                            <span class="detail-comment-time">${comment.time}</span>
                            <button class="detail-comment-reply-btn">
                                <span>💬</span>
                                <span>回复</span>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

            document.getElementById('detailCommentsList').innerHTML = commentsHtml;

            // 显示弹窗
            document.getElementById('detailModal').style.display = 'block';
            document.body.style.overflow = 'hidden';

            // 存储当前操作ID
            window.currentDetailActionId = actionId;
        }

        // 关闭详情弹窗
        function closeDetailModal() {
            document.getElementById('detailModal').style.display = 'none';
            document.body.style.overflow = 'auto';
            const input = document.getElementById('detailInput');
            input.value = '';
            input.style.height = 'auto';
        }

        // 发送详情评论
        function sendDetailComment() {
            const input = document.getElementById('detailInput');
            const content = input.value.trim();

            if (!content) {
                alert('请输入评论内容');
                return;
            }

            // 模拟发送评论
            const newComment = {
                avatar: '我',
                author: '我',
                text: content,
                time: '刚刚'
            };

            // 添加到评论列表
            const commentHtml = `
                <div class="detail-comment-item">
                    <div class="detail-comment-avatar">${newComment.avatar}</div>
                    <div class="detail-comment-content">
                        <div class="detail-comment-author">${newComment.author}</div>
                        <div class="detail-comment-text">${newComment.text}</div>
                        <div class="detail-comment-actions">
                            <span class="detail-comment-time">${newComment.time}</span>
                            <button class="detail-comment-reply-btn">
                                <span>💬</span>
                                <span>回复</span>
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('detailCommentsList').insertAdjacentHTML('beforeend', commentHtml);

            // 清空输入框并重置高度
            input.value = '';
            input.style.height = 'auto';

            // 滚动到底部
            const modalBody = document.querySelector('.detail-modal-body');
            modalBody.scrollTop = modalBody.scrollHeight;
        }

        // 获取操作数据（模拟）
        function getActionData(actionId) {
            const data = {
                1: {
                    avatar: '经',
                    author: '张经理',
                    role: '经理 · 账户管理部',
                    time: '2小时前',
                    content: '账户153需要进行资金调整，当前账户余额过高，建议进行分散管理。请在今日内完成以下操作：转出50%资金到备用账户，调整单笔交易限额至10000，开启自动止损功能。',
                    image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgZmlsbD0iIzMzMzMzMyIvPgogIDx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2ZmZmZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuW9k+WJjeW4guWcuuWbvuihqDwvdGV4dD4KPC9zdmc+'
                },
                2: {
                    avatar: '组',
                    author: '刘组长',
                    role: '组长 · 账户运营组',
                    time: '4小时前',
                    content: '批量账户需要进行安全升级，请协助完成所有子账户的密码重置和双重验证设置。优先处理高额度账户。',
                    image: null
                },
                3: {
                    avatar: '经',
                    author: '张经理',
                    role: '经理 · 账户管理部',
                    time: '1天前',
                    content: '本周账户运营总结：账户153资金调整已完成，备用账户激活成功。下周重点关注大额账户的交易行为监控。',
                    image: null
                }
            };
            return data[actionId] || data[1];
        }

        // 获取评论数据（模拟）
        function getActionComments(actionId) {
            const comments = {
                1: [
                    { avatar: '李', author: '李业务员', text: '收到，正在执行资金转移操作，预计30分钟内完成账户调整。', time: '1小时前' },
                    { avatar: '经', author: '张经理 > 李业务员', text: '好的，注意分批转移，不要一次性转移全部资金，确保账户正常运行。', time: '50分钟前' },
                    { avatar: '王', author: '王业务员', text: '经理，我负责的账户余额也比较高，是否也需要同步进行资金调整？', time: '45分钟前' },
                    { avatar: '经', author: '张经理 > 王业务员', text: '是的，所有高额度账户都需要调整。你负责的账户确实余额偏高，建议转出60%资金。', time: '30分钟前' },
                    { avatar: '赵', author: '赵业务员', text: '已完成账户153的资金转移，备用账户已激活，交易限额已调整。', time: '15分钟前' },
                    { avatar: '陈', author: '陈业务员', text: '自动止损功能已开启，系统运行正常。', time: '10分钟前' }
                ],
                2: [
                    { avatar: '陈', author: '陈业务员', text: '已经开始处理高额度账户的安全升级，预计今天能完成50%。', time: '3小时前' },
                    { avatar: '赵', author: '赵业务员', text: '组长，双重验证设置比较复杂，需要用户配合，进度可能会慢一些。', time: '2小时前' },
                    { avatar: '组', author: '刘组长 > 赵业务员', text: '理解，可以分批进行，先处理配合度高的用户。', time: '1小时前' },
                    { avatar: '李', author: '李业务员', text: '已完成20个账户的密码重置，用户反馈良好。', time: '30分钟前' }
                ],
                3: [
                    { avatar: '李', author: '李业务员', text: '这周账户运营确实很顺利，感谢经理的指导！', time: '1天前' },
                    { avatar: '王', author: '王业务员', text: '备用账户的激活流程很完善，用户体验不错。', time: '20小时前' },
                    { avatar: '陈', author: '陈业务员', text: '下周的监控重点我们已经准备好了，会重点关注异常交易。', time: '18小时前' },
                    { avatar: '经', author: '张经理 > 陈业务员', text: '很好，记得及时汇报异常情况。', time: '16小时前' }
                ]
            };
            return comments[actionId] || comments[1];
        }

        // 点击弹窗外部关闭
        document.getElementById('detailModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDetailModal();
            }
        });

        // 输入框自动调整高度
        document.getElementById('detailInput').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 200) + 'px';
        });

        // 回车发送评论（Shift+Enter换行）
        document.getElementById('detailInput').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendDetailComment();
            }
        });

        // 清空详情输入框
        function clearDetailInput() {
            const input = document.getElementById('detailInput');
            input.value = '';
            input.style.height = 'auto';
        }

        // 上传详情图片
        function uploadDetailImage() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    // 这里可以添加图片上传逻辑
                    alert('图片上传功能待实现');
                }
            };
            input.click();
        }

        // 为所有评论回复输入框添加自动调整高度功能
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.comment-reply-textarea').forEach(textarea => {
                textarea.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = Math.min(this.scrollHeight, 120) + 'px';
                });
            });
        });

        // 添加键盘事件监听
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                // 优先关闭弹窗
                if (document.getElementById('addModal').classList.contains('show')) {
                    hideAddModal();
                } else {
                    closeActionSidebar();
                    // 关闭所有回复框
                    document.querySelectorAll('.reply-form').forEach(form => {
                        form.classList.remove('show');
                    });
                }
            }
        });

        // 点击弹窗外部关闭弹窗
        document.getElementById('addModal').addEventListener('click', function(event) {
            if (event.target === this) {
                hideAddModal();
            }
        });

        // 阻止内部点击事件冒泡到父元素
        document.addEventListener('click', function(event) {
            if (event.target.closest('.comment-reply-btn') ||
                event.target.closest('.reply-btn') ||
                event.target.closest('.stat-item') ||
                event.target.closest('.comment-item') ||
                event.target.closest('.reply-item') ||
                event.target.closest('.reply-form') ||
                event.target.closest('.add-modal-content')) {
                event.stopPropagation();
            }
        });
    </script>
</body>
</html>
