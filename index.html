<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账号交易管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
            overflow-x: hidden;
        }

        .header {
            background-color: #2d2d2d;
            padding: 10px 20px;
            border-bottom: 1px solid #404040;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo {
            font-weight: bold;
            font-size: 16px;
        }

        .nav-tabs {
            display: flex;
            gap: 10px;
        }

        .nav-tab {
            padding: 8px 16px;
            background-color: #404040;
            border: none;
            color: #ffffff;
            border-radius: 4px;
            cursor: pointer;
        }

        .nav-tab.active {
            background-color: #4a90e2;
        }

        .sidebar {
            position: fixed;
            left: 0;
            top: 60px;
            width: 200px;
            height: calc(100vh - 60px);
            background-color: #2d2d2d;
            border-right: 1px solid #404040;
            padding: 20px 0;
        }

        .sidebar-item {
            padding: 10px 20px;
            cursor: pointer;
            border-left: 3px solid transparent;
        }

        .sidebar-item:hover {
            background-color: #404040;
        }

        .sidebar-item.active {
            background-color: #4a90e2;
            border-left-color: #ffffff;
        }

        .main-content {
            margin-left: 200px;
            padding: 20px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .search-bar {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .search-input {
            padding: 8px 12px;
            background-color: #404040;
            border: 1px solid #606060;
            color: #ffffff;
            border-radius: 4px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-primary {
            background-color: #4a90e2;
            color: #ffffff;
        }

        .btn-success {
            background-color: #5cb85c;
            color: #ffffff;
        }

        .btn-danger {
            background-color: #d9534f;
            color: #ffffff;
        }

        .btn-warning {
            background-color: #f0ad4e;
            color: #ffffff;
        }

        .table-container {
            background-color: #2d2d2d;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #404040;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #404040;
        }

        .table th {
            background-color: #404040;
            font-weight: 600;
        }

        .table tr:hover {
            background-color: #353535;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background-color: #5cb85c;
            color: #ffffff;
        }

        .action-btn {
            background-color: #4a90e2;
            color: #ffffff;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .action-btn:hover {
            background-color: #357abd;
        }

        /* 侧边栏样式 */
        .action-sidebar {
            position: fixed;
            right: -600px;
            top: 0;
            width: 600px;
            height: 100vh;
            background-color: #2d2d2d;
            border-left: 1px solid #404040;
            transition: right 0.3s ease;
            z-index: 1000;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.3);
        }

        .action-sidebar.open {
            right: 0;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-title {
            font-size: 18px;
            font-weight: 600;
        }

        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .add-btn {
            background-color: #4a90e2;
            color: #ffffff;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .add-btn:hover {
            background-color: #357abd;
        }

        .close-btn {
            background: none;
            border: none;
            color: #ffffff;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
        }

        .sidebar-content {
            padding: 0;
            height: calc(100vh - 80px);
            overflow-y: auto;
        }

        .action-item {
            border-bottom: 1px solid #404040;
            padding: 20px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            position: relative;
        }

        .action-item:hover {
            background-color: #353535;
        }

        .action-item.expanded {
            background-color: #353535;
        }

        .action-tag {
            position: absolute;
            top: 0;
            left: 0;
            width: 70px;
            height: 70px;
            overflow: hidden;
            z-index: 10;
            pointer-events: none;
        }

        .action-tag::after {
            content: attr(data-text);
            position: absolute;
            top: 12px;
            left: -15px;
            width: 80px;
            height: 18px;
            background-color: var(--tag-color);
            color: #ffffff;
            font-size: 10px;
            font-weight: 600;
            text-align: center;
            line-height: 18px;
            transform: rotate(-45deg);
            transform-origin: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .action-tag.strategy {
            --tag-color: #4a90e2;
        }

        .action-tag.feedback {
            --tag-color: #5cb85c;
        }

        .action-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 12px;
            margin-top: 24px;
        }

        .action-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #4a90e2;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            flex-shrink: 0;
        }

        .action-info {
            flex: 1;
        }

        .action-author {
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 4px;
        }

        .action-role {
            font-size: 12px;
            color: #888;
            margin-bottom: 8px;
        }

        .action-time {
            font-size: 12px;
            color: #888;
        }

        .action-content {
            margin-bottom: 12px;
        }

        .action-text {
            color: #ffffff;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .action-image {
            max-width: 100%;
            border-radius: 8px;
            margin-top: 8px;
        }

        .action-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #888;
        }

        .stat-left {
            display: flex;
            gap: 20px;
        }

        .stat-item {
            cursor: pointer;
        }

        .stat-item:hover {
            color: #4a90e2;
        }

        .reply-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            background: none;
            border: none;
            color: #888;
            cursor: pointer;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .reply-btn:hover {
            color: #4a90e2;
            background-color: #404040;
        }

        .comments-section {
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid #404040;
            display: none;
        }

        .comments-section.show {
            display: block;
        }

        .comment-item {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
            padding: 12px;
            background-color: #404040;
            border-radius: 8px;
        }

        .comment-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #5cb85c;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
            flex-shrink: 0;
        }

        .comment-content {
            flex: 1;
        }

        .comment-author {
            font-weight: 500;
            color: #ffffff;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .comment-text {
            color: #cccccc;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 8px;
        }

        .comment-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
        }

        .comment-time-info {
            color: #888;
        }

        .comment-reply-btn {
            display: flex;
            align-items: center;
            gap: 4px;
            background: none;
            border: none;
            color: #888;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .comment-reply-btn:hover {
            color: #4a90e2;
            background-color: #404040;
        }

        .reply-item {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
            padding: 12px;
            background-color: #404040;
            border-radius: 8px;
        }

        .reply-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-color: #5cb85c;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
            flex-shrink: 0;
        }

        .reply-content {
            flex: 1;
        }

        .reply-author {
            font-weight: 500;
            color: #ffffff;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .reply-target {
            color: #888;
            font-size: 14px;
        }

        .reply-text {
            color: #cccccc;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 8px;
        }

        .reply-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
        }

        .reply-time-info {
            color: #888;
        }

        .reply-reply-btn {
            display: flex;
            align-items: center;
            gap: 4px;
            background: none;
            border: none;
            color: #888;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .reply-reply-btn:hover {
            color: #4a90e2;
            background-color: #353535;
        }

        .view-more {
            text-align: center;
            padding: 12px;
            color: #4a90e2;
            cursor: pointer;
            font-size: 14px;
            border-top: 1px solid #404040;
        }

        .view-more:hover {
            background-color: #353535;
        }

        /* 回复输入框样式 */
        .reply-form {
            margin-top: 12px;
            padding: 16px;
            background-color: #404040;
            border-radius: 8px;
            border: 1px solid #606060;
            display: none;
        }

        .reply-form.show {
            display: block;
        }

        .reply-input {
            width: 100%;
            min-height: 80px;
            padding: 12px;
            background-color: #353535;
            border: 1px solid #606060;
            color: #ffffff;
            border-radius: 6px;
            resize: vertical;
            font-family: inherit;
            font-size: 14px;
            margin-bottom: 12px;
        }

        .reply-input:focus {
            outline: none;
            border-color: #4a90e2;
        }

        .reply-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .reply-tools {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .tool-btn {
            background: none;
            border: none;
            color: #888;
            cursor: pointer;
            padding: 6px;
            border-radius: 4px;
            font-size: 16px;
            transition: all 0.2s ease;
        }

        .tool-btn:hover {
            color: #4a90e2;
            background-color: #353535;
        }

        .reply-actions {
            display: flex;
            gap: 8px;
        }

        .reply-submit {
            background-color: #4a90e2;
            color: #ffffff;
            border: none;
            padding: 6px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
        }

        .reply-submit:hover {
            background-color: #357abd;
        }

        .reply-cancel {
            background: none;
            border: none;
            color: #888;
            cursor: pointer;
            padding: 6px 12px;
            font-size: 12px;
        }

        .reply-cancel:hover {
            color: #ffffff;
        }

        /* 新增策略动作弹窗样式 */
        .add-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 2000;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .add-modal.show {
            display: flex;
        }

        .add-modal-content {
            background-color: #2d2d2d;
            border-radius: 12px;
            width: 500px;
            max-width: 90vw;
            max-height: 80vh;
            overflow-y: auto;
            border: 1px solid #404040;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }

        .add-modal-header {
            padding: 20px;
            border-bottom: 1px solid #404040;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .add-modal-title {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
        }

        .add-modal-close {
            background: none;
            border: none;
            color: #ffffff;
            font-size: 20px;
            cursor: pointer;
            padding: 5px;
        }

        .add-modal-body {
            padding: 20px;
        }

        .add-form-group {
            margin-bottom: 20px;
        }

        .add-form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #ffffff;
        }

        .add-form-textarea {
            width: 100%;
            min-height: 120px;
            padding: 12px;
            background-color: #404040;
            border: 1px solid #606060;
            color: #ffffff;
            border-radius: 6px;
            resize: vertical;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.5;
        }

        .add-form-textarea:focus {
            outline: none;
            border-color: #4a90e2;
        }

        .add-form-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 16px;
        }

        .add-form-tools {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .add-tool-btn {
            background: none;
            border: none;
            color: #888;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            font-size: 18px;
            transition: all 0.2s ease;
        }

        .add-tool-btn:hover {
            color: #4a90e2;
            background-color: #404040;
        }

        .add-form-actions {
            display: flex;
            gap: 12px;
        }

        .add-submit-btn {
            background-color: #4a90e2;
            color: #ffffff;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }

        .add-submit-btn:hover {
            background-color: #357abd;
        }

        .add-cancel-btn {
            background: none;
            border: 1px solid #606060;
            color: #ffffff;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .add-cancel-btn:hover {
            background-color: #404040;
        }

        .add-image-preview {
            margin-top: 12px;
            max-width: 100%;
            border-radius: 6px;
            display: none;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .overlay.show {
            opacity: 1;
            visibility: visible;
        }
    </style>
</head>
<body>
    <!-- 头部导航 -->
    <div class="header">
        <div class="logo">🎯 先机系统</div>
        <div class="nav-tabs">
            <button class="nav-tab">首页</button>
            <button class="nav-tab">共享</button>
            <button class="nav-tab active">策略</button>
        </div>
    </div>

    <!-- 左侧边栏 -->
    <div class="sidebar">
        <div class="sidebar-item">📊 数据分析</div>
        <div class="sidebar-item">🔍 策略分析</div>
        <div class="sidebar-item">🏠 首页</div>
        <div class="sidebar-item active">📈 持仓</div>
        <div class="sidebar-item">💰 资金</div>
        <div class="sidebar-item">📋 委托</div>
        <div class="sidebar-item">📊 小组</div>
        <div class="sidebar-item">⚙️ 设置</div>
        <div class="sidebar-item">📱 策略配置</div>
        <div class="sidebar-item">📊 持仓分析</div>
        <div class="sidebar-item">💹 基础分析</div>
        <div class="sidebar-item">📈 市场分析</div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
        <div class="content-header">
            <div class="search-bar">
                <input type="text" class="search-input" placeholder="搜索账号...">
                <button class="btn btn-primary">搜索</button>
            </div>
            <div>
                <button class="btn btn-success">新增</button>
                <button class="btn btn-danger">删除</button>
                <button class="btn btn-warning">导出</button>
            </div>
        </div>

        <div class="table-container">
            <table class="table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>账号名称</th>
                        <th>账号类型</th>
                        <th>账号状态</th>
                        <th>交易类型</th>
                        <th>账号余额</th>
                        <th>可用余额</th>
                        <th>冻结金额</th>
                        <th>今日盈亏</th>
                        <th>总盈亏</th>
                        <th>最后交易时间</th>
                        <th>操作</th>
                        <th>账号指令</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>153</td>
                        <td>💰 账户_120000_2024...</td>
                        <td><span class="status-badge status-active">主账户</span></td>
                        <td><span class="status-badge status-active">正常</span></td>
                        <td><span class="status-badge status-active">现货</span></td>
                        <td>120000.00</td>
                        <td>118500.00</td>
                        <td>1500.00</td>
                        <td>+2500.00</td>
                        <td>📈 +8500.00</td>
                        <td>2025-07-04 10:40:26</td>
                        <td>
                            <button class="btn btn-primary btn-sm">编辑</button>
                            <button class="btn btn-danger btn-sm">删除</button>
                        </td>
                        <td>
                            <button class="action-btn" onclick="openActionSidebar()">账号指令</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 遮罩层 -->
    <div class="overlay" id="overlay" onclick="closeActionSidebar()"></div>

    <!-- 策略动作侧边栏 -->
    <div class="action-sidebar" id="actionSidebar">
        <div class="sidebar-header">
            <h3 class="sidebar-title">账号操作指令</h3>
            <div class="header-actions">
                <button class="add-btn" onclick="showAddModal()">新增</button>
                <button class="close-btn" onclick="closeActionSidebar()">&times;</button>
            </div>
        </div>
        <div class="sidebar-content">
            <!-- 策略动作1 -->
            <div class="action-item" onclick="toggleComments(1)">
                <div class="action-tag strategy" data-text="操作指令"></div>
                <div class="action-header">
                    <div class="action-avatar">经</div>
                    <div class="action-info">
                        <div class="action-author">张经理</div>
                        <div class="action-role">经理 · 账户管理部</div>
                    </div>
                    <div class="action-time">2小时前</div>
                </div>
                <div class="action-content">
                    <div class="action-text">账户153需要进行资金调整，当前账户余额过高，建议进行分散管理。请在今日内完成以下操作：转出50%资金到备用账户，调整单笔交易限额至10000，开启自动止损功能。</div>
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyMCIgZmlsbD0iIzMzMzMzMyIvPgogIDx0ZXh0IHg9IjEwMCIgeT0iNjAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2ZmZmZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuW9k+WJjeW4guWcuuWbvuihqDwvdGV4dD4KPC9zdmc+" alt="市场图表" class="action-image">
                </div>
                <div class="action-stats">
                    <div class="stat-left">
                        <span class="stat-item">💬 3条回复</span>
                    </div>
                    <button class="reply-btn" onclick="showActionReply(1, event)">
                        <span>💬</span>
                        <span>回复</span>
                    </button>
                </div>
                <div class="comments-section" id="comments-1">
                    <div class="comment-item">
                        <div class="comment-avatar">李</div>
                        <div class="comment-content">
                            <div class="comment-author">李业务员</div>
                            <div class="comment-text">收到，正在执行资金转移操作，预计30分钟内完成账户调整。</div>
                            <div class="comment-actions">
                                <span class="comment-time-info">1小时前</span>
                                <button class="comment-reply-btn" onclick="showCommentReply(this, event)">
                                    <span>💬</span>
                                    <span>回复</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="comment-item">
                        <div class="comment-avatar">经</div>
                        <div class="comment-content">
                            <div class="comment-author">张经理 > 李业务员</div>
                            <div class="comment-text">好的，注意分批转移，不要一次性转移全部资金，确保账户正常运行。</div>
                            <div class="comment-actions">
                                <span class="comment-time-info">50分钟前</span>
                                <button class="comment-reply-btn" onclick="showCommentReply(this, event)">
                                    <span>💬</span>
                                    <span>回复</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="comment-item">
                        <div class="comment-avatar">王</div>
                        <div class="comment-content">
                            <div class="comment-author">王业务员</div>
                            <div class="comment-text">经理，我负责的账户余额也比较高，是否也需要同步进行资金调整？</div>
                            <div class="comment-actions">
                                <span class="comment-time-info">45分钟前</span>
                                <button class="comment-reply-btn" onclick="showCommentReply(this, event)">
                                    <span>💬</span>
                                    <span>回复</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="comment-item">
                        <div class="comment-avatar">经</div>
                        <div class="comment-content">
                            <div class="comment-author">张经理 > 王业务员</div>
                            <div class="comment-text">是的，所有高额度账户都需要调整。你负责的账户确实余额偏高，建议转出60%资金。</div>
                            <div class="comment-actions">
                                <span class="comment-time-info">30分钟前</span>
                                <button class="comment-reply-btn" onclick="showCommentReply(this, event)">
                                    <span>💬</span>
                                    <span>回复</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 策略动作2 -->
            <div class="action-item" onclick="toggleComments(2)">
                <div class="action-tag strategy" data-text="操作指令"></div>
                <div class="action-header">
                    <div class="action-avatar">组</div>
                    <div class="action-info">
                        <div class="action-author">刘组长</div>
                        <div class="action-role">组长 · 账户运营组</div>
                    </div>
                    <div class="action-time">4小时前</div>
                </div>
                <div class="action-content">
                    <div class="action-text">批量账户需要进行安全升级，请协助完成所有子账户的密码重置和双重验证设置。优先处理高额度账户。</div>
                </div>
                <div class="action-stats">
                    <div class="stat-left">
                        <span class="stat-item">💬 5条回复</span>
                    </div>
                    <button class="reply-btn" onclick="showActionReply(2, event)">
                        <span>💬</span>
                        <span>回复</span>
                    </button>
                </div>
                <div class="comments-section" id="comments-2">
                    <div class="comment-item">
                        <div class="comment-avatar">陈</div>
                        <div class="comment-content">
                            <div class="comment-author">陈业务员</div>
                            <div class="comment-text">已经关注到几只AI概念股，准备小仓位试水。</div>
                            <div class="comment-actions">
                                <span class="comment-time-info">3小时前</span>
                                <button class="comment-reply-btn" onclick="showCommentReply(this, event)">
                                    <span>💬</span>
                                    <span>回复</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="comment-item">
                        <div class="comment-avatar">赵</div>
                        <div class="comment-content">
                            <div class="comment-author">赵业务员</div>
                            <div class="comment-text">组长，具体推荐哪几只股票？我这边资金比较充裕。</div>
                            <div class="comment-actions">
                                <span class="comment-time-info">2小时前</span>
                                <button class="comment-reply-btn" onclick="showCommentReply(this, event)">
                                    <span>💬</span>
                                    <span>回复</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 策略动作3 -->
            <div class="action-item" onclick="toggleComments(3)">
                <div class="action-tag feedback" data-text="跟进反馈"></div>
                <div class="action-header">
                    <div class="action-avatar">经</div>
                    <div class="action-info">
                        <div class="action-author">张经理</div>
                        <div class="action-role">经理 · 账户管理部</div>
                    </div>
                    <div class="action-time">1天前</div>
                </div>
                <div class="action-content">
                    <div class="action-text">本周账户运营总结：账户153资金调整已完成，备用账户激活成功。下周重点关注大额账户的交易行为监控。</div>
                </div>
                <div class="action-stats">
                    <div class="stat-left">
                        <span class="stat-item">💬 8条回复</span>
                    </div>
                    <button class="reply-btn" onclick="showActionReply(3, event)">
                        <span>💬</span>
                        <span>回复</span>
                    </button>
                </div>
                <div class="comments-section" id="comments-3">
                    <div class="comment-item">
                        <div class="comment-avatar">李</div>
                        <div class="comment-content">
                            <div class="comment-author">李业务员</div>
                            <div class="comment-text">这周确实收益不错，感谢经理的指导！</div>
                            <div class="comment-actions">
                                <span class="comment-time-info">1天前</span>
                                <button class="comment-reply-btn" onclick="showCommentReply(this, event)">
                                    <span>💬</span>
                                    <span>回复</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="view-more" onclick="openDetailPage()">
            查看更多反馈
        </div>
    </div>

    <!-- 新增策略动作弹窗 -->
    <div class="add-modal" id="addModal">
        <div class="add-modal-content">
            <div class="add-modal-header">
                <h3 class="add-modal-title">新增内容</h3>
                <button class="add-modal-close" onclick="hideAddModal()">&times;</button>
            </div>
            <div class="add-modal-body">
                <div class="add-form-group">
                    <label class="add-form-label">类型选择</label>
                    <div style="display: flex; gap: 16px; margin-bottom: 16px;">
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="radio" name="actionType" value="strategy" checked style="margin: 0;">
                            <span style="color: #4a90e2;">操作指令</span>
                        </label>
                        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                            <input type="radio" name="actionType" value="feedback" style="margin: 0;">
                            <span style="color: #5cb85c;">跟进反馈</span>
                        </label>
                    </div>
                </div>
                <div class="add-form-group">
                    <label class="add-form-label">内容</label>
                    <textarea class="add-form-textarea" id="addActionContent" placeholder="请输入具体内容..."></textarea>
                    <img id="addImagePreview" class="add-image-preview" alt="图片预览">
                </div>
                <div class="add-form-toolbar">
                    <div class="add-form-tools">
                        <button class="add-tool-btn" onclick="uploadAddImage()" title="上传图片">📷</button>
                        <button class="add-tool-btn" title="表情">😊</button>
                        <button class="add-tool-btn" title="提及">@</button>
                        <input type="file" id="addImageUpload" accept="image/*" style="display: none;" onchange="handleAddImageUpload(this)">
                    </div>
                    <div class="add-form-actions">
                        <button class="add-cancel-btn" onclick="hideAddModal()">取消</button>
                        <button class="add-submit-btn" onclick="submitAddAction()">发布</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openActionSidebar() {
            document.getElementById('actionSidebar').classList.add('open');
            document.getElementById('overlay').classList.add('show');
        }

        function closeActionSidebar() {
            document.getElementById('actionSidebar').classList.remove('open');
            document.getElementById('overlay').classList.remove('show');
            // 关闭所有展开的评论
            document.querySelectorAll('.comments-section').forEach(section => {
                section.classList.remove('show');
            });
            document.querySelectorAll('.action-item').forEach(item => {
                item.classList.remove('expanded');
            });
        }

        function toggleComments(actionId) {
            const commentsSection = document.getElementById(`comments-${actionId}`);
            const actionItem = commentsSection.closest('.action-item');

            // 关闭其他展开的评论
            document.querySelectorAll('.comments-section').forEach(section => {
                if (section.id !== `comments-${actionId}`) {
                    section.classList.remove('show');
                }
            });
            document.querySelectorAll('.action-item').forEach(item => {
                if (item !== actionItem) {
                    item.classList.remove('expanded');
                }
            });

            // 切换当前评论的显示状态
            commentsSection.classList.toggle('show');
            actionItem.classList.toggle('expanded');

            // 阻止事件冒泡
            event.stopPropagation();
        }

        function openDetailPage() {
            // 打开新标签页显示详细的反馈页面
            const detailUrl = 'detail.html';
            window.open(detailUrl, '_blank');
        }

        function showActionReply(actionId, event) {
            event.stopPropagation();

            // 关闭其他回复框
            document.querySelectorAll('.reply-form').forEach(form => {
                form.classList.remove('show');
            });

            // 查找或创建回复框
            const actionItem = event.target.closest('.action-item');
            let replyForm = actionItem.querySelector('.reply-form');

            if (!replyForm) {
                replyForm = createReplyForm();
                actionItem.appendChild(replyForm);
            }

            replyForm.classList.add('show');
            replyForm.querySelector('.reply-input').focus();
        }

        function showCommentReply(button, event) {
            event.stopPropagation();

            // 关闭其他回复框
            document.querySelectorAll('.reply-form').forEach(form => {
                form.classList.remove('show');
            });

            // 查找或创建回复框
            const commentItem = button.closest('.comment-item');
            let replyForm = commentItem.querySelector('.reply-form');

            if (!replyForm) {
                replyForm = createReplyForm();
                commentItem.appendChild(replyForm);
            }

            replyForm.classList.add('show');
            const input = replyForm.querySelector('.reply-input');
            const author = commentItem.querySelector('.comment-author').textContent;
            input.placeholder = `回复 ${author}...`;
            input.focus();
        }

        function createReplyForm() {
            const form = document.createElement('div');
            form.className = 'reply-form';
            form.innerHTML = `
                <textarea class="reply-input" placeholder="写下你的回复..."></textarea>
                <div class="reply-toolbar">
                    <div class="reply-tools">
                        <button class="tool-btn" onclick="uploadImage(this)" title="上传图片">📷</button>
                        <button class="tool-btn" title="表情">😊</button>
                        <button class="tool-btn" title="提及">@</button>
                    </div>
                    <div class="reply-actions">
                        <button class="reply-cancel" onclick="hideReplyForm(this)">取消</button>
                        <button class="reply-submit" onclick="submitReply(this)">发布</button>
                    </div>
                </div>
                <input type="file" accept="image/*" style="display: none;" onchange="handleImageUpload(this)">
            `;
            return form;
        }

        function hideReplyForm(button) {
            const form = button.closest('.reply-form');
            form.classList.remove('show');
            form.querySelector('.reply-input').value = '';
        }

        function submitReply(button) {
            const form = button.closest('.reply-form');
            const input = form.querySelector('.reply-input');
            const text = input.value.trim();

            if (!text) {
                alert('请输入回复内容');
                return;
            }

            // 模拟提交
            alert('回复已发布！');
            hideReplyForm(button);
        }

        function uploadImage(button) {
            const form = button.closest('.reply-form');
            const fileInput = form.querySelector('input[type="file"]');
            fileInput.click();
        }

        function handleImageUpload(input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];
                alert(`图片 "${file.name}" 已选择，将在发布时上传`);
            }
        }

        // 新增策略动作相关函数
        function showAddModal() {
            document.getElementById('addModal').classList.add('show');
            document.body.style.overflow = 'hidden';
            document.getElementById('addActionContent').focus();
        }

        function hideAddModal() {
            document.getElementById('addModal').classList.remove('show');
            document.body.style.overflow = 'auto';
            // 清空表单
            document.getElementById('addActionContent').value = '';
            document.getElementById('addImagePreview').style.display = 'none';
            document.getElementById('addImageUpload').value = '';
        }

        function uploadAddImage() {
            document.getElementById('addImageUpload').click();
        }

        function handleAddImageUpload(input) {
            const preview = document.getElementById('addImagePreview');
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        function submitAddAction() {
            const content = document.getElementById('addActionContent').value.trim();
            const actionType = document.querySelector('input[name="actionType"]:checked').value;
            const typeName = actionType === 'strategy' ? '操作指令' : '跟进反馈';

            if (!content) {
                alert('请输入内容');
                return;
            }

            // 模拟提交
            alert(`${typeName}已发布！`);
            hideAddModal();

            // 这里可以添加实际的提交逻辑
            // 例如：将新的内容添加到侧边栏列表中
            // addNewActionToSidebar(content, actionType);
        }

        // 可选：动态添加新内容到侧边栏的函数
        function addNewActionToSidebar(content, type) {
            const sidebarContent = document.querySelector('.sidebar-content');
            const tagClass = type === 'strategy' ? 'strategy' : 'feedback';
            const tagText = type === 'strategy' ? '操作指令' : '跟进反馈';
            const actionId = Date.now(); // 简单的ID生成

            const newActionHTML = `
                <div class="action-item" onclick="toggleComments(${actionId})">
                    <div class="action-tag ${tagClass}" data-text="${tagText}"></div>
                    <div class="action-header">
                        <div class="action-avatar">我</div>
                        <div class="action-info">
                            <div class="action-author">我</div>
                            <div class="action-role">当前用户</div>
                            <div class="action-time">刚刚</div>
                        </div>
                    </div>
                    <div class="action-content">
                        <div class="action-text">${content}</div>
                    </div>
                    <div class="action-stats">
                        <div class="stat-left">
                            <span class="stat-item">💬 0条回复</span>
                        </div>
                        <button class="reply-btn" onclick="showActionReply(${actionId}, event)">
                            <span>💬</span>
                            <span>回复</span>
                        </button>
                    </div>
                    <div class="comments-section" id="comments-${actionId}" style="display: none;">
                        <div class="comments-list">
                            <!-- 暂无评论 -->
                        </div>
                    </div>
                </div>
            `;

            sidebarContent.insertAdjacentHTML('afterbegin', newActionHTML);
        }

        // 添加键盘事件监听
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                // 优先关闭弹窗
                if (document.getElementById('addModal').classList.contains('show')) {
                    hideAddModal();
                } else {
                    closeActionSidebar();
                    // 关闭所有回复框
                    document.querySelectorAll('.reply-form').forEach(form => {
                        form.classList.remove('show');
                    });
                }
            }
        });

        // 点击弹窗外部关闭弹窗
        document.getElementById('addModal').addEventListener('click', function(event) {
            if (event.target === this) {
                hideAddModal();
            }
        });

        // 阻止内部点击事件冒泡到父元素
        document.addEventListener('click', function(event) {
            if (event.target.closest('.comment-reply-btn') ||
                event.target.closest('.reply-btn') ||
                event.target.closest('.stat-item') ||
                event.target.closest('.comment-item') ||
                event.target.closest('.reply-item') ||
                event.target.closest('.reply-form') ||
                event.target.closest('.add-modal-content')) {
                event.stopPropagation();
            }
        });
    </script>
</body>
</html>
